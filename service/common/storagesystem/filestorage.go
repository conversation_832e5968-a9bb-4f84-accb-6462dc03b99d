package storagesystem

import (
	"context"
	"fmt"
	"github.com/minio/minio-go/v7"
	"strings"
	"time"
)

type FileStorageEngine interface {
	PreSignedPutUrl(ctx context.Context, bucket, object, contentType string, expireDuration time.Duration) (string, error)
	PreSignedGetUrl(ctx context.Context, bucket, object string, expireDuration time.Duration) (string, error)
	CopyFile(ctx context.Context, srcBucket, srcObject, destBucket, destObject string) error
	GetUrl(ctx context.Context, bucket, object string) string
	Delete(ctx context.Context, bucket, object string) error
	UploadLocalFile(ctx context.Context, bucket, object, contentType, filePath string) error
	Name() string
}

type FileStorageSystem struct {
	Settings *StorageSettings
}

func (fs *FileStorageSystem) NewUploadFile(ctx context.Context, fileId string, contentType string) (*FileObject, error) {
	engine, err := fs.createDefaultStorage(ctx)
	if err != nil {
		return nil, err
	}
	return &FileObject{
		object:      fmt.Sprintf("%s/%s", fs.Settings.UploadPrefix, fileId),
		Engine:      engine,
		contentType: contentType,
		bucket:      fs.Settings.Bucket,
	}, nil
}

func (fs *FileStorageSystem) createDefaultStorage(ctx context.Context) (FileStorageEngine, error) {
	s := fs.Settings
	if s.S3Endpoint != "" {
		s3, err := NewS3FileStorage(s.S3Endpoint, s.S3AccessKeyID, s.S3SecretKey)
		if err != nil {
			return nil, fmt.Errorf("failed to create S3 storage: %w", err)
		}
		exists, errBucketExists := s3.Client.BucketExists(ctx, fs.Settings.Bucket)
		if errBucketExists != nil {
			return nil, errBucketExists
		}
		if !exists {
			err = s3.Client.MakeBucket(ctx, fs.Settings.Bucket, minio.MakeBucketOptions{})
			if err != nil {
				return nil, err
			}
		}
		return s3, nil
	} else {
		gcs, err := NewGCSFileStorage(s.GcsServiceAccount)
		if err != nil {
			return nil, fmt.Errorf("failed to create GCS storage: %w", err)
		}
		if _, err := gcs.Client.Bucket(s.Bucket).Attrs(ctx); err != nil {
			return nil, fmt.Errorf("bucket %s does not exist: %w", s.Bucket, err)
		}
		return gcs, nil
	}
}

func (fs *FileStorageSystem) GetUploadFileByID(engine FileStorageEngine, fileID string) *FileObject {
	return &FileObject{
		Engine: engine,
		bucket: fs.Settings.Bucket,
		object: fmt.Sprintf("%s/%s", fs.Settings.UploadPrefix, fileID),
	}
}

func (fs *FileStorageSystem) FinalizeUpload(ctx context.Context, fileID string) (*FileObject, error) {
	storage, err := fs.createDefaultStorage(ctx)
	if err != nil {
		return nil, err
	}
	file := fs.GetUploadFileByID(storage, fileID)
	destObject := fmt.Sprintf("%s/%s", fs.Settings.Prefix, fileID)
	err = file.MoveTo(ctx, fs.Settings.Bucket, destObject)
	if err != nil {
		return nil, fmt.Errorf("finalize upload failed: %w", err)
	}

	return &FileObject{
		Engine: storage,
		bucket: fs.Settings.Bucket,
		object: destObject,
	}, err
}

func (fs *FileStorageSystem) GetFromUrl(ctx context.Context, url string) (*FileObject, error) {
	protocol, rest, found := strings.Cut(url, "://")
	if !found {
		return nil, fmt.Errorf("invalid storage URL: %s", url)
	}
	bucket, object, _ := strings.Cut(rest, "/")
	s := fs.Settings
	if protocol == "gcs" {
		storage, err := NewGCSFileStorage(s.GcsServiceAccount)
		if err != nil {
			return nil, err
		}
		return &FileObject{
			bucket: bucket,
			object: object,
			Engine: storage,
		}, nil
	} else if protocol == "s3" {
		if s.S3Endpoint == "" {
			return nil, fmt.Errorf("S3 storage is not  configured")
		}
		storage, err := NewS3FileStorage(s.S3Endpoint, s.S3AccessKeyID, s.S3SecretKey)
		return &FileObject{
			bucket: bucket,
			object: object,
			Engine: storage,
		}, err
	} else {
		return nil, fmt.Errorf("unsupported storage protocol: %s", protocol)
	}
}

type FileObject struct {
	Engine      FileStorageEngine `json:"-"`
	object      string
	bucket      string
	contentType string
}

func (f *FileObject) PreSignedUploadUrl(ctx context.Context, expireDuration time.Duration) (string, error) {
	return f.Engine.PreSignedPutUrl(ctx, f.bucket, f.object, f.contentType, expireDuration)
}

func (f *FileObject) PreSignedDownloadUrl(ctx context.Context, expireDuration time.Duration) (string, error) {
	return f.Engine.PreSignedGetUrl(ctx, f.bucket, f.object, expireDuration)
}

func (f *FileObject) MoveTo(ctx context.Context, destBucket, destObject string) error {
	if err := f.Engine.CopyFile(ctx, f.bucket, f.object, destBucket, destObject); err != nil {
		return err
	}
	return f.Engine.Delete(ctx, f.bucket, f.object)
}

func (f *FileObject) GetUrl(ctx context.Context) string {
	return f.Engine.GetUrl(ctx, f.bucket, f.object)
}

func (f *FileObject) Delete(ctx context.Context) error {
	return f.Engine.Delete(ctx, f.bucket, f.object)
}

func (f *FileObject) GetObject() string {
	return f.object
}

func (f *FileObject) GetBucket() string {
	return f.bucket
}

func (f *FileObject) UploadLocalFile(ctx context.Context, filePath string) error {
	return f.Engine.UploadLocalFile(ctx, f.bucket, f.object, f.contentType, filePath)
}
