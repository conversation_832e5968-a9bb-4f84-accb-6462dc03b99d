package storagesystem

import (
	"cloud.google.com/go/storage"
	"context"
	"fmt"
	"io"
	"os"
	"sentioxyz/sentio/common/log"
	"time"
)

const GcsURLPrefix = "gcs://"

type GCSFileStorage struct {
	gcsServiceAccount string
	Client            *storage.Client
}

func NewGCSFileStorage(gcsServiceAccount string) (*GCSFileStorage, error) {
	ctx := context.Background()
	client, err := storage.NewClient(ctx)
	if err != nil {
		return nil, err
	}
	return &GCSFileStorage{
		gcsServiceAccount: gcsServiceAccount,
		Client:            client,
	}, nil
}

func (g *GCSFileStorage) Name() string {
	return "gcs"
}

func (g *GCSFileStorage) PreSignedPutUrl(ctx context.Context, bucket, fileUrl, contentType string, expireDuration time.Duration) (string, error) {

	gcsClient := g.Client

	opts := &storage.SignedURLOptions{
		GoogleAccessID: g.gcsServiceAccount,
		Method:         "PUT",
		Expires:        time.Now().Add(expireDuration),
		Headers: []string{
			"Content-Type:" + contentType,
		}, Scheme: storage.SigningSchemeV4,
	}
	return gcsClient.Bucket(bucket).SignedURL(fileUrl, opts)
}

func (g *GCSFileStorage) PreSignedGetUrl(ctx context.Context, bucket, fileUrl string, expireDuration time.Duration) (string, error) {
	gcsClient := g.Client
	opts := &storage.SignedURLOptions{
		GoogleAccessID: g.gcsServiceAccount,
		Method:         "GET",
		Expires:        time.Now().Add(expireDuration),
		Scheme:         storage.SigningSchemeV4,
	}
	return gcsClient.Bucket(bucket).SignedURL(fileUrl, opts)
}

func (g *GCSFileStorage) CopyFile(ctx context.Context, srcBucket, srcFileUrl, destBucket, destFileUrl string) error {
	src := g.Client.Bucket(srcBucket).Object(srcFileUrl)
	dst := g.Client.Bucket(destBucket).Object(destFileUrl)
	copier := dst.CopierFrom(src)
	if _, err := copier.Run(ctx); err != nil {
		return err
	}

	return nil
}

func (g *GCSFileStorage) GetUrl(ctx context.Context, bucket, fileUrl string) string {
	return fmt.Sprintf("%s%s/%s", GcsURLPrefix, bucket, fileUrl)
}

func (g *GCSFileStorage) Delete(ctx context.Context, bucket, fileUrl string) error {
	gcsClient, err := storage.NewClient(ctx)
	if err != nil {
		log.Fatale(err)
	}

	obj := gcsClient.Bucket(bucket).Object(fileUrl)
	if err := obj.Delete(ctx); err != nil {
		return fmt.Errorf("failed to delete object %s from bucket %s: %w", fileUrl, bucket, err)
	}
	return nil
}

func (g *GCSFileStorage) UploadLocalFile(ctx context.Context, bucket, object, contentType, filePath string) error {
	file, err := os.Open(filePath)
	if err != nil {
		return err
	}
	defer file.Close()

	writer := g.Client.Bucket(bucket).Object(object).NewWriter(ctx)
	if contentType != "" {
		writer.ContentType = contentType
	}

	_, err = io.Copy(writer, file)
	if err != nil {
		writer.Close()
		return err
	}

	return writer.Close()
}
