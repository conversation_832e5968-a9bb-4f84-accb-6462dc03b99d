package main

import (
	"context"
	"flag"
	"fmt"
	"os"

	"sentioxyz/sentio/common/flags"
	"sentioxyz/sentio/common/log"
	"sentioxyz/sentio/common/monitoring"
	"sentioxyz/sentio/service/analytic/clients"
	"sentioxyz/sentio/service/common/auth"
	"sentioxyz/sentio/service/common/repository"
	"sentioxyz/sentio/service/common/rpc"
	"sentioxyz/sentio/service/exports/models"
	"sentioxyz/sentio/service/exports/protos"
	"sentioxyz/sentio/service/exports/service"

	"google.golang.org/grpc/reflection"
)

func main() {
	var (
		dbURL = flag.String(
			"database",
			"postgres://postgres:postgres@localhost:5432/postgres",
			"The postgres database address",
		)
		port          = flag.Int("port", 10050, "The server port")
		authIssuerURL = flag.String(
			"auth-issuer-url",
			"https://sentio-dev.us.auth0.com/",
			"The auth0 issue url",
		)
		authAudience = flag.String("auth-audience", "http://localhost:8080/v1", "The auth0 audience")
		authClientID = flag.String(
			"auth-client-id",
			"JREam3EysMTM49eFbAjNK02OCykpmda3",
			"The auth0 app clientId",
		)
		clickhouseConfigPath = flag.String("clickhouse-config-path", "", "Clickhouse config path")
		_                    = flag.String("system-table-config-path",
			"service/analytic/data/system_table.yaml",
			"System table config path")

		exportFilePath    = flag.String("export-file-path", "/tmp/exports", "Export file path")
		gcsServiceAccount = flag.String(
			"gcs-service-account",
			"<EMAIL>",
			"Service account for GCS",
		)
		gcsBucket = flag.String(
			"gcs-bucket",
			"sentio-exports",
			"The GCS bucket for processor code",
		)
		sendgridAPIKey         = flag.String("sendgrid-api-key", os.Getenv("SENDGRID_API_KEY"), "The sendgrid api key")
		clickhouseRewriterAddr = flag.String("clickhouse-rewriter-address",
			"clickhouse-rewriter-sql-rewriter.clickhouse-premium:50051",
			"Clickhouse rewriter address")
		clickhouseMvDisplay = flag.Bool("clickhouse-mv-display", false, "enable display clickhouse mv on editor")
		k8sContextUse       = flag.String("k8s-context-use", "sentio-sea", "The k8s context use")
		ipfsNodeAddr        = flag.String("ipfs", "localhost:5001", "address of the ipfs node")
		s3endpoint          = flag.String("s3-endpoint", "", "S3 endpoint URL (http://host:port or https://host:port), if not set, will use GCS")
		s3accessKey         = flag.String("s3-access-key", "", "S3 access key ID")
		s3secretKey         = flag.String("s3-secret-key", "", "S3 secret key")
	)
	flags.ParseAndInitLogFlag()

	monitoring.StartMonitoring()
	defer monitoring.StopMonitoring()

	grpcSever := rpc.NewServer(true)
	conn, err := repository.SetupDBWithoutCache(*dbURL, &models.ExportTask{})

	if err != nil {
		log.Fatale(err)
	}

	authConfig := auth.AuthConfig{
		IssuerURL: *authIssuerURL,
		Audience:  *authAudience,
		ClientID:  *authClientID,
	}

	authManager := auth.NewAuthManager(&authConfig, conn)

	fileService := service.NewFileUploader(*gcsServiceAccount, *gcsBucket, *s3endpoint, *s3accessKey, *s3secretKey)
	emailSender := service.NewNotifier(*sendgridAPIKey, conn)
	ckRewriterClient, err := clients.NewRewriterClient(*clickhouseRewriterAddr)
	if err != nil {
		log.Warnf("failed to create rewriter client: %v", err)
	}
	queue := service.NewQueue(conn, *clickhouseConfigPath, *exportFilePath, authManager, fileService,
		emailSender, ckRewriterClient, *clickhouseMvDisplay, *k8sContextUse, *ipfsNodeAddr)
	queue.Start(1)
	defer queue.Stop()
	exportsService := service.NewService(
		conn,
		authManager,
		queue,
	)
	protos.RegisterExportsServiceServer(grpcSever, exportsService)
	reflection.Register(grpcSever)

	mux := rpc.NewServeMux()
	err = mux.HandlePath("GET", "/healthz", rpc.HealthCheck(conn))
	if err != nil {
		log.Fatale(err)
	}

	err = protos.RegisterExportsServiceHandlerFromEndpoint(context.Background(),
		mux,
		fmt.Sprintf(":%d", *port),
		rpc.GRPCGatewayDialOptions)
	if err != nil {
		log.Fatale(err)
	}

	rpc.BindAndServeWithHTTP(mux, grpcSever, *port, nil)
}
