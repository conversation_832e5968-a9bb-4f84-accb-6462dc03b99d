package service

import (
	"context"
	"os"
	"sentioxyz/sentio/common/log"
	"sentioxyz/sentio/service/analytic/models"
	"sentioxyz/sentio/service/common/storagesystem"
	"time"
)

type FileUploader struct {
	fileStorageSystem *storagesystem.FileStorageSystem
}

func NewFileUploader(gcsServiceAccount string, gcsBucket string, s3endpoint string, s3AccessKeyID string, s3SecretKey string) *FileUploader {

	fileStorageSystem := &storagesystem.FileStorageSystem{
		Settings: &storagesystem.StorageSettings{
			S3Endpoint:        s3endpoint,
			S3AccessKeyID:     s3AccessKeyID,
			S3SecretKey:       s3SecretKey,
			GcsServiceAccount: gcsServiceAccount,
			Bucket:            gcsBucket,
			UploadPrefix:      "upload",
			Prefix:            "exports",
		},
	}

	return &FileUploader{
		fileStorageSystem: fileStorageSystem,
	}
}

func (s *FileUploader) UploadFile(ctx context.Context, params *models.ExportSQLParams, uploadPath string) (string, error) {
	// Create file object using the FileStorageSystem
	fileObject, err := s.fileStorageSystem.NewUploadFile(ctx, uploadPath, "application/octet-stream")
	if err != nil {
		return "", err
	}

	// Upload the file directly from local path
	err = fileObject.UploadLocalFile(ctx, params.Filepath)
	if err != nil {
		return "", err
	}

	// Generate signed download URL
	url, err := fileObject.PreSignedDownloadUrl(ctx, 72*time.Hour)
	if err != nil {
		return "", err
	}

	// Remove the local file
	err = os.Remove(params.Filepath)
	if err != nil {
		return "", err
	}

	log.Infof("Upload file done, url: %s", url)
	return url, nil
}
